/* Toast Container */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 400px;
  width: 100%;
  pointer-events: none;
}

/* Individual Toast */
.toast {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-bottom: 12px;
  overflow: hidden;
  pointer-events: auto;
  position: relative;
  transform: translateX(100%);
  animation: slideIn 0.3s ease-out forwards;
  border-left: 4px solid;
}

/* Toast Animation */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Toast Content */
.toast-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

/* Toast Icon */
.toast-icon {
  font-size: 18px;
  font-weight: bold;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: white;
}

/* Toast Message */
.toast-message {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  font-weight: 500;
}

/* Close Button */
.toast-close {
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.toast-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #333;
}

/* Progress Bar */
.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  width: 100%;
  animation: progress linear forwards;
}

@keyframes progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Toast Types */
.toast-success {
  border-left-color: #28a745;
}

.toast-success .toast-icon {
  background-color: #28a745;
}

.toast-success .toast-progress {
  background-color: #28a745;
}

.toast-error {
  border-left-color: #dc3545;
}

.toast-error .toast-icon {
  background-color: #dc3545;
}

.toast-error .toast-progress {
  background-color: #dc3545;
}

.toast-warning {
  border-left-color: #ffc107;
}

.toast-warning .toast-icon {
  background-color: #ffc107;
  color: #333;
}

.toast-warning .toast-progress {
  background-color: #ffc107;
}

.toast-info {
  border-left-color: #17a2b8;
}

.toast-info .toast-icon {
  background-color: #17a2b8;
}

.toast-info .toast-progress {
  background-color: #17a2b8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .toast-content {
    padding: 12px;
  }
  
  .toast-message {
    font-size: 13px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .toast {
    background: #2d3748;
    color: #e2e8f0;
  }
  
  .toast-message {
    color: #e2e8f0;
  }
  
  .toast-close {
    color: #a0aec0;
  }
  
  .toast-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
  }
}
