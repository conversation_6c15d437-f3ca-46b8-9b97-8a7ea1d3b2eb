.home-container {
    min-height: calc(100vh - 70px);
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

/* Loading State */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: calc(100vh - 70px);
    color: #666;
}

.spinner-large {
    width: 50px;
    height: 50px;
    border: 4px solid #e1e5e9;
    border-radius: 50%;
    border-top-color: #667eea;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Welcome Section */
.welcome-section {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 40px;
    align-items: center;
    background: white;
    border-radius: 16px;
    padding: 40px;
    margin-bottom: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.welcome-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin: 0 0 10px 0;
    line-height: 1.2;
}

.welcome-subtitle {
    font-size: 1.2rem;
    color: #666;
    margin: 0;
}

.welcome-illustration {
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-avatar-large {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    font-weight: 700;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: flex-start;
    gap: 20px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.stat-content h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
    margin: 0 0 8px 0;
}

.stat-description {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

/* Info Section */
.info-section {
    margin-bottom: 40px;
}

.info-card {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e1e5e9;
}

.info-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.verified-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #d4edda;
    color: #155724;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.info-content {
    display: grid;
    gap: 16px;
}

.info-row {
    display: grid;
    grid-template-columns: 150px 1fr;
    gap: 16px;
    align-items: center;
}

.info-label {
    font-weight: 500;
    color: #666;
}

.info-value {
    color: #333;
    font-weight: 500;
}

/* Actions Section */
.actions-section {
    margin-bottom: 40px;
}

.actions-section h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 24px 0;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.action-card {
    background: white;
    border: none;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.action-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.action-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-bottom: 16px;
}

.action-card h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
}

.action-card p {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

/* Error State */
.error-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: calc(100vh - 70px);
    text-align: center;
}

.error-content h2 {
    color: #333;
    margin-bottom: 16px;
}

.error-content p {
    color: #666;
    max-width: 400px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        padding: 20px 15px;
    }

    .welcome-section {
        grid-template-columns: 1fr;
        text-align: center;
        padding: 30px 20px;
    }

    .welcome-content h1 {
        font-size: 2rem;
    }

    .user-avatar-large {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .info-row {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .info-label {
        font-size: 0.9rem;
    }

    .actions-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .welcome-content h1 {
        font-size: 1.8rem;
    }

    .stat-card {
        padding: 20px;
    }

    .info-card {
        padding: 20px;
    }

    .action-card {
        padding: 20px;
    }
}