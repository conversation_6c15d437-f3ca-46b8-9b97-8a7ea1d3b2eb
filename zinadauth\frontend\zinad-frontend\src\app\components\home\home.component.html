<app-navbar></app-navbar>

<div class="home-container">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="spinner-large"></div>
    <p>Loading your dashboard...</p>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading && currentUser" class="main-content">
    <!-- Welcome Section -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1>{{ getTimeOfDay() }}, {{ currentUser.name }}! 👋</h1>
        <p class="welcome-subtitle">Welcome to your ZinadAuth dashboard</p>
      </div>
      <div class="welcome-illustration">
        <div class="user-avatar-large">
          {{ currentUser.name.charAt(0).toUpperCase() }}
        </div>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
        </div>
        <div class="stat-content">
          <h3>Profile Status</h3>
          <p class="stat-value">Active</p>
          <p class="stat-description">Your account is verified and active</p>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="16" y1="2" x2="16" y2="6"></line>
            <line x1="8" y1="2" x2="8" y2="6"></line>
            <line x1="3" y1="10" x2="21" y2="10"></line>
          </svg>
        </div>
        <div class="stat-content">
          <h3>Member Since</h3>
          <p class="stat-value">{{ getJoinDate() }}</p>
          <p class="stat-description">Thank you for being with us!</p>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M9 12l2 2 4-4"></path>
            <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
            <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
            <path d="M13 12h3a2 2 0 0 1 2 2v1"></path>
            <path d="M13 12h-3a2 2 0 0 0-2 2v1"></path>
          </svg>
        </div>
        <div class="stat-content">
          <h3>Security</h3>
          <p class="stat-value">Protected</p>
          <p class="stat-description">
            Your account is secure with JWT authentication
          </p>
        </div>
      </div>
    </div>

    <!-- User Information Card -->
    <div class="info-section">
      <div class="info-card">
        <div class="info-header">
          <h2>Account Information</h2>
          <span class="verified-badge">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M9 12l2 2 4-4"></path>
              <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
              <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
              <path d="M13 12h3a2 2 0 0 1 2 2v1"></path>
              <path d="M13 12h-3a2 2 0 0 0-2 2v1"></path>
            </svg>
            Verified
          </span>
        </div>
        <div class="info-content">
          <div class="info-row">
            <span class="info-label">Full Name:</span>
            <span class="info-value">{{ currentUser.name }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Email Address:</span>
            <span class="info-value">{{ currentUser.email }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Account ID:</span>
            <span class="info-value">#{{ currentUser.id }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Last Updated:</span>
            <span class="info-value">{{
              currentUser.updated_at | date : "medium"
            }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="actions-section">
      <h2>Quick Actions</h2>
      <div class="actions-grid">
        <button class="action-card">
          <div class="action-icon">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
          </div>
          <h3>Edit Profile</h3>
          <p>Update your personal information</p>
        </button>

        <button class="action-card">
          <div class="action-icon">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
              <circle cx="12" cy="16" r="1"></circle>
              <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
            </svg>
          </div>
          <h3>Security Settings</h3>
          <p>Manage your account security</p>
        </button>

        <button class="action-card">
          <div class="action-icon">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path
                d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"
              ></path>
              <polyline points="14,2 14,8 20,8"></polyline>
            </svg>
          </div>
          <h3>Download Data</h3>
          <p>Export your account data</p>
        </button>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && !currentUser" class="error-container">
    <div class="error-content">
      <h2>Unable to load dashboard</h2>
      <p>
        Please try refreshing the page or contact support if the problem
        persists.
      </p>
    </div>
  </div>
</div>
