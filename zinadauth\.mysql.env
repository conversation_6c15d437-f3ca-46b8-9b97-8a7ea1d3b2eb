# MySQL Database Configuration for ZinadAuth
# These environment variables are used by the MySQL Docker container

# Root password for MySQL (change this in production)
MYSQL_ROOT_PASSWORD=zinadauth_root_2024

# Database name for the application
MYSQL_DATABASE=zinadauth_db

# Application database user
MYSQL_USER=zinadauth_user

# Application database user password (change this in production)
MYSQL_PASSWORD=zinadauth_pass_2024

# MySQL configuration
MYSQL_CHARSET=utf8mb4
MYSQL_COLLATION=utf8mb4_unicode_ci

# Security settings
MYSQL_ROOT_HOST=%

# Performance settings
MYSQL_INNODB_BUFFER_POOL_SIZE=256M
MYSQL_MAX_CONNECTIONS=100
