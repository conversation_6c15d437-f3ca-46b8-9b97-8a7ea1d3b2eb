.navbar {
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.navbar-brand {
    cursor: pointer;
    transition: transform 0.2s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.navbar-brand h3 {
    margin: 0;
    color: #667eea;
    font-weight: 700;
    font-size: 24px;
}

.navbar-nav {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* User Menu Styles */
.user-menu {
    position: relative;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.user-info:hover {
    background-color: #f8f9fa;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 16px;
}

.user-name {
    color: #333;
    font-weight: 500;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.dropdown-icon {
    color: #666;
    transition: transform 0.2s ease;
}

.dropdown-icon.rotated {
    transform: rotate(180deg);
}

/* Dropdown Menu */
.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    min-width: 250px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    z-index: 1001;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    padding: 20px;
    border-bottom: 1px solid #e1e5e9;
}

.user-details {
    text-align: left;
}

.user-name-large {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.user-email {
    color: #666;
    font-size: 14px;
}

.dropdown-divider {
    height: 1px;
    background-color: #e1e5e9;
    margin: 0;
}

.dropdown-item {
    width: 100%;
    padding: 12px 20px;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #333;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item svg {
    color: #666;
}

/* Guest Menu */
.guest-menu {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    border: none;
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 2px solid transparent;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Backdrop */
.dropdown-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
    background: transparent;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar-container {
        padding: 0 15px;
    }

    .user-name {
        display: none;
    }

    .guest-menu {
        gap: 8px;
    }

    .btn {
        padding: 8px 16px;
        font-size: 13px;
    }

    .dropdown-menu {
        min-width: 200px;
        right: -10px;
    }
}