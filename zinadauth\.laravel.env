# Laravel Environment Configuration for Docker
# These variables override the backend/.env file when running in Docker

# Application Environment
APP_ENV=production
APP_DEBUG=false

# Database Configuration (matches MySQL container)
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=zinadauth_db
DB_USERNAME=zinadauth_user
DB_PASSWORD=zinadauth_pass_2024

# Cache and Session Configuration for Docker
CACHE_DRIVER=file
SESSION_DRIVER=file
QUEUE_CONNECTION=sync

# Redis Configuration (if needed later)
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail Configuration (configure as needed)
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="ZinadAuth"

# Docker-specific settings
LOG_CHANNEL=stderr
LOG_STDERR_FORMATTER=Monolog\Formatter\JsonFormatter

# File permissions for Docker
WWWGROUP=1000
WWWUSER=1000
