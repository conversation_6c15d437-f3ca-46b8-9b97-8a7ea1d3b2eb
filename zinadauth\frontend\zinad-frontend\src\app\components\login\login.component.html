<div class="login-container">
  <div class="login-card">
    <div class="login-header">
      <h2>Welcome Back</h2>
      <p>Sign in to your account</p>
    </div>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
      <!-- Email Field -->
      <div class="form-group">
        <label for="email">Email Address</label>
        <input
          type="email"
          id="email"
          formControlName="email"
          class="form-control"
          [class.is-invalid]="f['email'].invalid && f['email'].touched"
          placeholder="Enter your email"
        />
        <div
          class="invalid-feedback"
          *ngIf="f['email'].invalid && f['email'].touched"
        >
          <div *ngIf="f['email'].errors?.['required']">Email is required</div>
          <div *ngIf="f['email'].errors?.['email']">
            Please enter a valid email
          </div>
        </div>
      </div>

      <!-- Password Field -->
      <div class="form-group">
        <label for="password">Password</label>
        <input
          type="password"
          id="password"
          formControlName="password"
          class="form-control"
          [class.is-invalid]="f['password'].invalid && f['password'].touched"
          placeholder="Enter your password"
        />
        <div
          class="invalid-feedback"
          *ngIf="f['password'].invalid && f['password'].touched"
        >
          <div *ngIf="f['password'].errors?.['required']">
            Password is required
          </div>
          <div *ngIf="f['password'].errors?.['minlength']">
            Password must be at least 6 characters
          </div>
        </div>
      </div>

      <!-- Success Message -->
      <div class="alert alert-success" *ngIf="successMessage">
        {{ successMessage }}
      </div>

      <!-- Error Message -->
      <div class="alert alert-danger" *ngIf="errorMessage">
        {{ errorMessage }}
      </div>

      <!-- Submit Button -->
      <button
        type="submit"
        class="btn btn-primary btn-block"
        [disabled]="isLoading"
      >
        <span *ngIf="isLoading" class="spinner"></span>
        {{ isLoading ? "Signing in..." : "Sign In" }}
      </button>
    </form>

    <!-- Register Link -->
    <div class="login-footer">
      <p>
        Don't have an account?
        <a
          href="javascript:void(0)"
          (click)="navigateToRegister(); $event.preventDefault()"
          class="register-link"
          >Sign up here</a
        >
      </p>
    </div>
  </div>
</div>
